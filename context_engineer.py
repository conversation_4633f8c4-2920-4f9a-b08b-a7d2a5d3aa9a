import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import json
import os
from openai import OpenAI
import threading

class ContextEngineerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Context Engineer - AI Prompt Optimizer")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Initialize OpenAI client
        self.client = None
        self.api_key = ""
        
        # Create main interface
        self.create_widgets()
        self.load_settings()
        
    def create_widgets(self):
        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        
        # Title
        title_label = ttk.Label(main_frame, text="Context Engineer - AI Prompt Optimizer", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # API Key Frame
        api_frame = ttk.LabelFrame(main_frame, text="API Configuration", padding=10)
        api_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(api_frame, text="OpenRouter API Key:").pack(anchor=tk.W)
        self.api_key_entry = ttk.Entry(api_frame, show="*", width=50)
        self.api_key_entry.pack(fill=tk.X, pady=(5, 10))
        
        api_button_frame = ttk.Frame(api_frame)
        api_button_frame.pack(fill=tk.X)
        
        ttk.Button(api_button_frame, text="Save API Key", command=self.save_api_key).pack(side=tk.LEFT)
        ttk.Button(api_button_frame, text="Test Connection", command=self.test_connection).pack(side=tk.LEFT, padx=(10, 0))
        
        # Main content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Input
        left_frame = ttk.LabelFrame(content_frame, text="Original Prompt", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(left_frame, wrap=tk.WORD, height=20, font=('Consolas', 11))
        self.input_text.pack(fill=tk.BOTH, expand=True)
        
        # Input buttons
        input_button_frame = ttk.Frame(left_frame)
        input_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(input_button_frame, text="Load from File", command=self.load_prompt).pack(side=tk.LEFT)
        ttk.Button(input_button_frame, text="Clear", command=self.clear_input).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(input_button_frame, text="Optimize Prompt", command=self.optimize_prompt).pack(side=tk.RIGHT)
        
        # Right panel - Output
        right_frame = ttk.LabelFrame(content_frame, text="Optimized Prompt", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, height=20, font=('Consolas', 11))
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # Output buttons
        output_button_frame = ttk.Frame(right_frame)
        output_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(output_button_frame, text="Copy to Clipboard", command=self.copy_output).pack(side=tk.LEFT)
        ttk.Button(output_button_frame, text="Save to File", command=self.save_output).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(output_button_frame, text="Use as Input", command=self.use_as_input).pack(side=tk.RIGHT)
        
        # Bottom frame - Options and Status
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Options frame
        options_frame = ttk.LabelFrame(bottom_frame, text="Optimization Options", padding=10)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        options_grid = ttk.Frame(options_frame)
        options_grid.pack(fill=tk.X)
        
        # Optimization type
        ttk.Label(options_grid, text="Optimization Type:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.optimization_type = ttk.Combobox(options_grid, values=[
            "General Improvement",
            "Clarity Enhancement", 
            "Context Addition",
            "Specificity Boost",
            "Creative Enhancement",
            "Technical Precision"
        ], state="readonly", width=20)
        self.optimization_type.set("General Improvement")
        self.optimization_type.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # Target audience
        ttk.Label(options_grid, text="Target Audience:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.target_audience = ttk.Combobox(options_grid, values=[
            "General",
            "Technical",
            "Creative",
            "Business",
            "Academic",
            "Beginner"
        ], state="readonly", width=15)
        self.target_audience.set("General")
        self.target_audience.grid(row=0, column=3, sticky=tk.W)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(bottom_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X)
        
    def save_api_key(self):
        self.api_key = self.api_key_entry.get().strip()
        if self.api_key:
            # Save to settings file
            settings = {"api_key": self.api_key}
            with open("settings.json", "w") as f:
                json.dump(settings, f)
            
            # Initialize client
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=self.api_key,
            )
            
            self.status_var.set("API Key saved successfully")
            messagebox.showinfo("Success", "API Key saved successfully!")
        else:
            messagebox.showerror("Error", "Please enter a valid API key")
    
    def load_settings(self):
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", "r") as f:
                    settings = json.load(f)
                    self.api_key = settings.get("api_key", "")
                    self.api_key_entry.insert(0, self.api_key)
                    
                    if self.api_key:
                        self.client = OpenAI(
                            base_url="https://openrouter.ai/api/v1",
                            api_key=self.api_key,
                        )
        except Exception as e:
            print(f"Error loading settings: {e}")
    
    def test_connection(self):
        if not self.client:
            messagebox.showerror("Error", "Please save API key first")
            return
        
        def test():
            try:
                self.status_var.set("Testing connection...")
                completion = self.client.chat.completions.create(
                    model="openrouter/horizon-alpha",
                    messages=[{"role": "user", "content": "Hello, this is a test message."}],
                    max_tokens=10
                )
                self.status_var.set("Connection successful!")
                messagebox.showinfo("Success", "API connection is working!")
            except Exception as e:
                self.status_var.set("Connection failed")
                messagebox.showerror("Error", f"Connection failed: {str(e)}")
        
        threading.Thread(target=test, daemon=True).start()
    
    def optimize_prompt(self):
        if not self.client:
            messagebox.showerror("Error", "Please configure API key first")
            return
        
        original_prompt = self.input_text.get("1.0", tk.END).strip()
        if not original_prompt:
            messagebox.showerror("Error", "Please enter a prompt to optimize")
            return
        
        def optimize():
            try:
                self.status_var.set("Optimizing prompt...")
                
                optimization_instructions = self.get_optimization_instructions()
                
                system_prompt = f"""You are an expert prompt engineer. Your task is to improve the given prompt for better AI interaction.

{optimization_instructions}

Please provide ONLY the improved prompt without any explanations or additional text."""

                completion = self.client.chat.completions.create(
                    model="openrouter/horizon-alpha",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": f"Original prompt to optimize:\n\n{original_prompt}"}
                    ],
                    max_tokens=2000,
                    temperature=0.7
                )
                
                optimized_prompt = completion.choices[0].message.content.strip()
                
                # Update output text in main thread
                self.root.after(0, lambda: self.update_output(optimized_prompt))
                self.root.after(0, lambda: self.status_var.set("Prompt optimized successfully!"))
                
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set("Optimization failed"))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Optimization failed: {str(e)}"))
        
        threading.Thread(target=optimize, daemon=True).start()
    
    def get_optimization_instructions(self):
        opt_type = self.optimization_type.get()
        audience = self.target_audience.get()
        
        instructions = {
            "General Improvement": "Improve clarity, specificity, and effectiveness of the prompt.",
            "Clarity Enhancement": "Focus on making the prompt clearer and more understandable.",
            "Context Addition": "Add relevant context and background information to improve results.",
            "Specificity Boost": "Make the prompt more specific and detailed in its requirements.",
            "Creative Enhancement": "Enhance the creative aspects and encourage innovative responses.",
            "Technical Precision": "Improve technical accuracy and precision of the prompt."
        }
        
        audience_notes = {
            "General": "Keep language accessible to general audience.",
            "Technical": "Use technical terminology and assume technical knowledge.",
            "Creative": "Encourage creative and artistic expression.",
            "Business": "Focus on business value and professional outcomes.",
            "Academic": "Use academic tone and scholarly approach.",
            "Beginner": "Use simple language and provide clear explanations."
        }
        
        return f"{instructions[opt_type]} Target audience: {audience_notes[audience]}"
    
    def update_output(self, text):
        self.output_text.delete("1.0", tk.END)
        self.output_text.insert("1.0", text)
    
    def load_prompt(self):
        file_path = filedialog.askopenfilename(
            title="Load Prompt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.input_text.delete("1.0", tk.END)
                self.input_text.insert("1.0", content)
                self.status_var.set(f"Loaded: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load file: {str(e)}")
    
    def save_output(self):
        content = self.output_text.get("1.0", tk.END).strip()
        if not content:
            messagebox.showerror("Error", "No content to save")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save Optimized Prompt",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_var.set(f"Saved: {os.path.basename(file_path)}")
                messagebox.showinfo("Success", "File saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
    
    def copy_output(self):
        content = self.output_text.get("1.0", tk.END).strip()
        if content:
            self.root.clipboard_clear()
            self.root.clipboard_append(content)
            self.status_var.set("Copied to clipboard")
            messagebox.showinfo("Success", "Content copied to clipboard!")
        else:
            messagebox.showerror("Error", "No content to copy")
    
    def use_as_input(self):
        content = self.output_text.get("1.0", tk.END).strip()
        if content:
            self.input_text.delete("1.0", tk.END)
            self.input_text.insert("1.0", content)
            self.status_var.set("Output moved to input")
        else:
            messagebox.showerror("Error", "No content to move")
    
    def clear_input(self):
        self.input_text.delete("1.0", tk.END)
        self.status_var.set("Input cleared")

def main():
    root = tk.Tk()
    app = ContextEngineerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()