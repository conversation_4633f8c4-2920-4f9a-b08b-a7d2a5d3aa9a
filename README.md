# Context Engineer - AI Prompt Optimizer

Modern Python uygulaması ile AI promptlarınızı optimize edin ve context engineering tekniklerini kullanarak daha iyi sonuçlar alın.

## Özellikler

- **Modern Arayüz**: Kullanıcı dostu tkinter tabanlı GUI
- **OpenRouter AI Entegrasyonu**: Horizon Alpha modeli ile prompt optimizasyonu
- **Çoklu Optimizasyon Türleri**: 
  - Genel İyileştirme
  - Netlik Artırma
  - Context Ekleme
  - Spesifiklik Artırma
  - Yaratıcılık Geliştirme
  - Teknik Hassasiyet
- **Hedef Kitle Seçimi**: Genel, Teknik, Yaratıcı, İş, Akademik, Başlangıç
- **Dosya İşlemleri**: Prompt yükleme ve kaydetme
- **Clipboard Desteği**: Hızlı kopyalama ve yapıştırma

## Kurulum

1. Gerekli kütüphaneleri yükleyin:
```bash
pip install -r requirements.txt
```

2. OpenRouter API anahtarınızı alın: https://openrouter.ai/

3. Uygulamayı çalıştırın:
```bash
python context_engineer.py
```

## Kullanım

1. **API Anahtarı Ayarlama**: 
   - OpenRouter API anahtarınızı "API Configuration" bölümüne girin
   - "Save API Key" butonuna tıklayın
   - "Test Connection" ile bağlantıyı test edin

2. **Prompt Optimizasyonu**:
   - Sol panele optimize etmek istediğiniz promptu yazın
   - Optimizasyon türünü ve hedef kitleyi seçin
   - "Optimize Prompt" butonuna tıklayın
   - Optimize edilmiş prompt sağ panelde görünecek

3. **Dosya İşlemleri**:
   - "Load from File": Dosyadan prompt yükle
   - "Save to File": Optimize edilmiş promptu kaydet
   - "Copy to Clipboard": Panoya kopyala
   - "Use as Input": Çıktıyı girdi olarak kullan

## API Konfigürasyonu

Uygulama OpenRouter API'sini kullanır. API anahtarınız güvenli bir şekilde yerel olarak saklanır.

```python
client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="YOUR_API_KEY",
)
```

## Özelleştirme

Farklı AI modelleri kullanmak için `context_engineer.py` dosyasındaki model parametresini değiştirebilirsiniz:

```python
model="openrouter/horizon-alpha"  # Diğer modeller için değiştirin
```

## Güvenlik

- API anahtarları yerel olarak şifrelenerek saklanır
- Hassas veriler internet üzerinden güvenli şekilde iletilir
- Ayarlar dosyası (`settings.json`) yerel olarak tutulur

## Sorun Giderme

- **Bağlantı Hatası**: API anahtarınızın doğru olduğundan emin olun
- **Yavaş Yanıt**: İnternet bağlantınızı kontrol edin
- **Hata Mesajları**: Status bar'da detaylı hata bilgileri görüntülenir

## Lisans

Bu proje MIT lisansı altında dağıtılmaktadır.